{"name": "vite-vue3-starter", "type": "module", "version": "1.0.0", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/chansee97/vite-vue3-starter", "repository": {"type": "git", "url": "git+https://github.com/chansee97/vite-vue3-starter.git"}, "bugs": {"url": "https://github.com/chansee97/vite-vue3-starter/issues"}, "keywords": ["vue", "unocss", "vite", "vitesse"], "scripts": {"dev": "vite --mode dev --port 5200", "dev:test": "vite --mode test ", "dev:prod": "vite --mode prod ", "build": "vite build --mode prod", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "test:e2e": "cypress open", "sizecheck": "npx vite-bundle-visualizer", "postinstall": "npx simple-git-hooks"}, "dependencies": {"@unocss/reset": "^66.4.1", "@vueuse/core": "^13.6.0", "axios": "^1.11.0", "nprogress": "^0.2.0", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^5.1.0", "@iconify-json/icon-park-outline": "^1.2.2", "@types/node": "^24.2.0", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "eslint": "^9.32.0", "lint-staged": "^16.1.4", "simple-git-hooks": "^2.13.1", "typescript": "^5.9.2", "unocss": "^66.4.1", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^28.8.0", "unplugin-vue-router": "^0.15.0", "vite": "^7.0.6", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-pwa": "^1.0.2", "vite-plugin-vue-layouts": "^0.11.0", "vue-tsc": "^3.0.5"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}