{"name": "vite-vue3-starter", "type": "module", "version": "0.1.0", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/chansee97/vite-vue3-starter", "repository": {"type": "git", "url": "git+https://github.com/chansee97/vite-vue3-starter.git"}, "bugs": {"url": "https://github.com/chansee97/vite-vue3-starter/issues"}, "keywords": ["vue", "unocss", "vite", "vitesse"], "scripts": {"dev": "vite --mode dev --port 5200", "dev:test": "vite --mode test ", "dev:prod": "vite --mode prod ", "build": "vite build --mode prod", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "test:e2e": "cypress open", "sizecheck": "npx vite-bundle-visualizer", "postinstall": "npx simple-git-hooks"}, "dependencies": {"@unocss/reset": "^0.58.5", "@vueuse/core": "^10.9.0", "axios": "^1.6.7", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@antfu/eslint-config": "^2.8.1", "@iconify-json/icon-park-outline": "^1.1.15", "@types/node": "^20.11.26", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "eslint": "^8.57.0", "lint-staged": "^15.2.2", "naive-ui": "^2.38.1", "simple-git-hooks": "^2.10.0", "typescript": "^5.4.2", "unocss": "^0.58.5", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "unplugin-vue-router": "^0.8.4", "vite": "^5.1.6", "vite-bundle-visualizer": "^1.0.1", "vite-plugin-pwa": "^0.19.2", "vite-plugin-vue-layouts": "^0.11.0", "vue-tsc": "^2.0.6"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}