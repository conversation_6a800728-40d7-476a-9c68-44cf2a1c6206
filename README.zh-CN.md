<div align='center'>
  <img src="/public/favicon.svg" width="150"/>
</div>

<div align='center'>
  <h1>vite-vue3-starter</h1>
  <h4>Virtuoso</h4>
</div>

<div align='center' >
  一个基于Vite, Unocss的Vue3快速启动模板
</div>

<div align='center' style="margin:2em 0;">
  <a href="https://virtuoso.vercel.app">在线演示</a>
</div>

<div align='center' style="margin:2em 0;">

  [英语](./README.md) | <b>简体中文</b>
</div>

## 特点
- 支持TS、TSX格式
- Simple-git-hooks + lint-staged + eslint + stylelint
- 基于文件的路由和布局
- 功能封装完好 [Axios](https://github.com/axios/axios)
- [Unocss](https://unocss.dev/) - 即时按需原子CSS引擎
- [NaiveUI](https://www.naiveui.com/zh-CN/light) - vue3组件库
- 支持PWA
- 使用 [Pinia](https://pinia.vuejs.org/) 进行状态管理
- [通过设置class使用任何图标集的图标](https://unocss.dev/presets/icons)
- 暗黑模式适配
- [组件自动引入](./src/components)
- [API自动引入](https://github.com/antfu/unplugin-auto-import) - 直接使用Composition和其他文件夹的API

## 用法
### GitHub 模板
[根据此模板在 GitHub 上创建一个 repo](https://github.com/chansee97/vite-vue3-starter/generate)

### 开发

```bash
# clone
git clone https://github.com/chansee97/vite-vue3-starter.git
# Installation of dependencies
pnpm i
# Development visit http://localhost:5200
pnpm dev
# Build
pnpm build
```
