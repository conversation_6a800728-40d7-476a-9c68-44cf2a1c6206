<div align='center'>
  <img src="/public/favicon.svg" width="150"/>
</div>

<div align='center'>
  <h1>vite-vue3-starter</h1>
  <h4>Virtuoso</h4>
</div>

<div align='center' >
  A quick start template for a vue3 project, based on Vite, Unocss
</div>

<div align='center'>
  <a href="https://virtuoso.vercel.app">Live Demo</a>
</div>

<div align='center'>

  <b>English</b> | [简体中文](./README.zh-CN.md)
</div>

## Features
- Support TypeScript, TSX
- Simple-git-hooks + lint-staged + eslint + stylelint
- File based routing and layouts
- Functionally wrapped [Axios](https://github.com/axios/axios)
- [Unocss](https://unocss.dev/) - the instant on-demand atomic CSS engine
- [NaiveUI](https://www.naiveui.com/zh-CN/light) - A Vue 3 Component Library
- PWA Support
- State Management via [Pinia](https://pinia.vuejs.org/)
- [Use icons from any icon sets with classes](https://unocss.dev/presets/icons)
- Dark mode adaptation
- [Components auto importing](./src/components)
- [APIs auto importing](https://github.com/antfu/unplugin-auto-import) - use Composition API and others directly

## Usage
### GitHub Template
[Create a repo from this template on GitHub.](https://github.com/chansee97/vite-vue3-starter/generate)

### Development

```bash
# clone
git clone https://github.com/chansee97/vite-vue3-starter.git
# Installation of dependencies
pnpm i
# Development visit http://localhost:5200
pnpm dev
# Build
pnpm build
```
