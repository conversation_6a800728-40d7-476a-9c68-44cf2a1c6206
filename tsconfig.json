{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "vite-plugin-vue-layouts/client", "vite-plugin-pwa/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules"]}