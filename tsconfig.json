{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "vite-plugin-vue-layouts/client", "vite-plugin-pwa/client", "unplugin-vue-router/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/types/typed-router.d.ts"], "exclude": ["dist", "node_modules", "eslint.config.js"]}